<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SupplementTypeConstant } from '@haierbusiness-front/common-libs'; // 🔧 新增：导入补充条目枚举
import { formatNumberThousands } from '@haierbusiness-front/utils';

// 类型定义
interface RelatedBillItem {
  id: string;
  sequenceNumber: number | undefined | null;
  date: string | undefined | null;
  project: string | undefined | null;
  category: string | undefined | null;
  contractPrice: number | undefined | null;
  contractQuantity: number | undefined | null;
  billPrice: number | string | undefined | null;
  billQuantity: number | undefined | null;
  relatedBill: string | undefined | null;
  itemData?: any;
}

// 🔧 新增：已有关联账单的数据结构
interface ExistingRelatedBillItem {
  id: string;
  amount: number;
  name: string;
  originalId: string;
  type: string;
  itemData: any;
}

interface RelatedBillDialogProps {
  visible: boolean;
  billType: 'invoice' | 'waterBill';
  billData: any;
  demandInfo?: any; // 需求信息，包含 stays 数据
  existingRelatedBills?: ExistingRelatedBillItem[]; // 🔧 修改：已有的关联账单数据
  excludedBillIds?: string[]; // 需要排除的账单ID（已被其他发票/水单关联）
  initialAttachmentAmount?: number; // 🔧 新增：初始附件金额
}

interface RelatedBillDialogEmits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: any): void;
  (
    e: 'updateStaysInvoiceId',
    data: {
      invoiceTempId: string;
      billType: 'invoice' | 'waterBill';
      selectedItemsByType: {
        stays: string[];
        places: string[];
        caterings: string[];
        vehicles: string[];
        attendants: string[];
        activities: string[];
        materials: string[]; // 🔧 新增：布展物料
        presents: string[]; // 🔧 新增：礼品
        others: string[]; // 🔧 新增：其他
        serviceFees: string[]; // 🔧 新增：全单服务费
      };
    },
  ): void;
  (
    e: 'updateRelatedAmount',
    data: {
      invoiceTempId: string;
      billType: 'invoice' | 'waterBill';
      totalAmount: number;
      relatedBills: RelatedBillItem[];
    },
  ): void;
  (
    e: 'updateAttachmentAmount',
    data: {
      invoiceTempId: string;
      billType: 'invoice' | 'waterBill';
      attachmentAmount: number;
    },
  ): void; // 🔧 新增：附件金额更新事件
}

const props = defineProps<RelatedBillDialogProps>();
const emit = defineEmits<RelatedBillDialogEmits>();

// 响应式数据
const isAddMode = ref(false); // 是否为添加模式
const selectedRowKeys = ref<string[]>([]);
const loading = ref(false);
const editableAttachmentAmount = ref<number>(0); // 可编辑的附件总金额

// 🔧 重新设计：区分数据来源
const cachedRelatedBills = ref<RelatedBillItem[]>([]); // 缓存的关联账单（来自props）
const userAddedBills = ref<RelatedBillItem[]>([]); // 用户新增的关联账单
const addedBillIds = ref<string[]>([]); // 记录已添加到关联账单的原始数据ID

// 临时状态管理（用于取消操作）
const tempUserAddedBills = ref<RelatedBillItem[]>([]); // 临时的用户新增数据

// 分页相关
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
});

// 筛选条件
const filterForm = ref({
  hotelPlan: '',
  project: '',
  planDate: null as any, // 日期选择器返回的是 dayjs 对象
});

// 项目选项 - 基础选项
const baseProjectOptions = [
  { value: '住宿', label: '住宿' },
  { value: '会场', label: '会场' },
  { value: '用餐', label: '用餐' },
  { value: '用车', label: '用车' },
  { value: '服务人员', label: '服务人员' },
  { value: '拓展活动', label: '拓展活动' }, // 🔧 修改：活动改为拓展活动
  { value: '布展物料', label: '布展物料' }, // 🔧 新增：布展物料
  { value: '礼品', label: '礼品' }, // 🔧 新增
  { value: '其他', label: '其他' }, // 🔧 新增
  { value: '全单服务费', label: '全单服务费' }, // 🔧 新增
];

// 🔧 新增：动态项目选项（包含补充条目中的具体项目）
const projectOptions = computed(() => {
  const options = [...baseProjectOptions];

  // 🔧 添加补充条目中的具体项目名称（使用枚举）
  if (props.demandInfo?.additionalItems) {
    const additionalProjects = new Set<string>();

    props.demandInfo.additionalItems.forEach((item: any) => {
      // 🔧 使用枚举获取项目类型的文本显示
      const typeEnum = SupplementTypeConstant.ofType(item.type);
      const projectName = typeEnum ? typeEnum.desc : item.itemName || '补充项目';

      if (projectName && projectName.trim()) {
        additionalProjects.add(projectName.trim());
      }
    });

    // 将补充条目的项目添加到选项中
    additionalProjects.forEach((projectName) => {
      options.push({ value: projectName, label: projectName });
    });
  }

  return options;
});

// 已关联账单数据（初始为空，从后端加载）
const relatedBillList = ref<RelatedBillItem[]>([]);

// 可选择的账单数据（从 demandInfo.stays 生成）
const availableBillList = ref<RelatedBillItem[]>([]);

// 计算属性
const attachmentTotalAmount = computed(() => {
  return relatedBillList.value.reduce((sum, item) => sum + Number(item.billPrice) * item.billQuantity, 0);
});

const billTotalAmount = computed(() => {
  return relatedBillList.value.reduce((sum, item) => sum + Number(item.billPrice) * item.billQuantity, 0);
});

const modalTitle = computed(() => {
  if (isAddMode.value) {
    return '添加关联账单';
  }
  return `关联账单详情 - ${props.billType === 'invoice' ? '发票' : '水单'}`;
});

// 🔥 新增：判断是否为查看模式（基于发票/水单数据中是否有真实ID）
const isViewOnlyMode = computed(() => {
  // 如果发票/水单有真实的数字ID（不是临时ID），则认为是查看模式
  return props.billData && props.billData.id && typeof props.billData.id === 'number';
});

// 表格列定义
const viewColumns = computed(() => {
  const baseColumns = [
    { title: '序号', dataIndex: 'sequenceNumber', width: 60 },
    { title: '日期', dataIndex: 'date', width: 100 },
    { title: '项目', dataIndex: 'project', width: 80 },
    { title: '类别', dataIndex: 'category', width: 80 },
    { title: '竞价单价金额(元)', dataIndex: 'contractPrice', width: 150 },
    { title: '竞价数量', dataIndex: 'contractQuantity', width: 80 },
    { title: '账单单价金额(元)', dataIndex: 'billPrice', width: 150 },
    { title: '账单实际数量', dataIndex: 'billQuantity', width: 120 },
  ];

  // 🔥 只在非查看模式下显示操作列
  if (!isViewOnlyMode.value) {
    baseColumns.push({ title: '操作', dataIndex: 'action', width: 80, fixed: 'right' as any });
  }

  return baseColumns;
});

const addColumns = [
  { title: '序号', dataIndex: 'sequenceNumber', width: 60 },
  { title: '日期', dataIndex: 'date', width: 100 },
  { title: '项目', dataIndex: 'project', width: 80 },
  { title: '类别', dataIndex: 'category', width: 80 },
  { title: '竞价单价金额(元)', dataIndex: 'contractPrice', width: 120 },
  { title: '竞价数量', dataIndex: 'contractQuantity', width: 80 },
  { title: '账单单价金额(元)', dataIndex: 'billPrice', width: 120 },
  { title: '账单实际数量', dataIndex: 'billQuantity', width: 120 },
];

// 筛选后的数据
const filteredAvailableBillList = computed(() => {
  let list = availableBillList.value;
  console.log(availableBillList.value,"availableBillList.value");
  console.log(props.excludedBillIds,"props.excludedBillIds");
  
  // 过滤掉已被其他发票/水单关联的账单
  if (props.excludedBillIds && props.excludedBillIds.length > 0) {
    list = list.filter((item, index) => {
      return !props.excludedBillIds?.includes(item.id);
    });
  }

  if (filterForm.value.project) {
    list = list.filter((item) => item.project === filterForm.value.project);
  }

  if (filterForm.value.hotelPlan) {
    list = list.filter(
      (item) => item.project.includes(filterForm.value.hotelPlan) || item.category.includes(filterForm.value.hotelPlan),
    );
  }

  if (filterForm.value.planDate) {
    // 格式化选中的日期为 YYYY-MM-DD 格式
    const selectedDate = filterForm.value.planDate.format('YYYY-MM-DD');
    list = list.filter((item) => {
      // 使用统一的日期格式化函数
      const formattedItemDate = formatDate(item.date);
      return formattedItemDate === selectedDate;
    });
  }

  // 更新分页总数
  pagination.value.total = list.length;
  return list;
});

// 计算是否全选（基于当前页面）
const isAllSelected = computed(() => {
  const currentPageData = getCurrentPageData();
  return currentPageData.length > 0 && currentPageData.every((item) => selectedRowKeys.value.includes(item.id));
});

// 格式化日期为 YYYY-MM-DD 格式
const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  // 如果已经是 YYYY-MM-DD 格式，直接返回
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    return dateStr;
  }
  // 如果包含时间部分，只取日期部分
  if (dateStr.includes(' ')) {
    return dateStr.split(' ')[0];
  }
  // 其他格式尝试转换
  try {
    const date = new Date(dateStr);
    return date.toISOString().split('T')[0];
  } catch {
    return dateStr;
  }
};

// 获取当前页面数据
const getCurrentPageData = () => {
  const start = (pagination.value.current - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  return filteredAvailableBillList.value.slice(start, end);
};

// 🔥 新增：根据发票/水单ID自动筛选关联的账单数据
const getRelatedBillsByInvoiceId = (invoiceId: string | number, billType: 'invoice' | 'waterBill') => {
  const relatedBills: RelatedBillItem[] = [];
  let sequenceNumber = 1;

  // 根据发票/水单类型确定要匹配的字段名
  const matchField = billType === 'invoice' ? 'miceBillAttachmentInvoiceId' : 'miceBillAttachmentStatementId';

  // 遍历所有类型的数据，找出匹配的项目
  const dataTypes = [
    { key: 'stays', name: '住宿' },
    { key: 'places', name: '会场' },
    { key: 'caterings', name: '用餐' },
    { key: 'vehicles', name: '用车' },
    { key: 'attendants', name: '服务人员' },
    { key: 'activities', name: '拓展活动' }
  ];

  dataTypes.forEach(({ key, name }, typeIndex) => {
    if (props.demandInfo?.[key]) {
      props.demandInfo[key].forEach((item: any, itemIndex: number) => {
        // 检查是否匹配当前发票/水单ID
        if (item[matchField] && item[matchField].toString() === invoiceId.toString()) {

          relatedBills.push({
            id: `${key.slice(0, -1)}_${item.id || item.tempId || sequenceNumber}`,
            sequenceNumber: sequenceNumber++,
            date: formatDate(item.demandDate || ''),
            project: name,
            category: '原需求', // 🔧 修复：统一使用简洁的"原需求"格式
            contractPrice: getContractPriceByType(item, key),
            contractQuantity: getContractQuantityByType(item, key),
            billPrice: getBillPriceByType(item, key),
            billQuantity: getBillQuantityByType(item, key),
            relatedBill: '查看>>',
            itemData: item
          });
        }
      });
    }
  });

  return relatedBills;
};

// 🔥 新增：根据数据类型获取合同价格的辅助函数
const getContractPriceByType = (item: any, type: string): number => {
  switch (type) {
    case 'stays':
      return item.schemeUnitPrice || 0;
    case 'places':
      return Number((
        item.schemeUnitPlacePrice +
        (item.hasLed ? (item.schemeUnitLedPrice || 0) * item.schemeLedNum : 0) +
        (item.hasTea ? item.teaEachTotalPrice * item.schemePersonNum : 0)
      ));
    case 'caterings':
    case 'vehicles':
    case 'attendants':
    case 'activities':
      return item.schemeUnitPrice || 0;
    default:
      return 0;
  }
};

// 🔥 新增：根据数据类型获取合同数量的辅助函数
const getContractQuantityByType = (item: any, type: string): number => {
  switch (type) {
    case 'stays':
      return item.schemeRoomNum || 0;
    case 'places':
      return 1;
    case 'caterings':
    case 'attendants':
    case 'activities':
      return item.schemePersonNum || 0;
    case 'vehicles':
      return item.schemeVehicleNum || 0;
    default:
      return 1;
  }
};

// 🔥 新增：根据数据类型获取账单价格的辅助函数
const getBillPriceByType = (item: any, type: string): number => {
  switch (type) {
    case 'stays':
      return item.billUnitPrice || 0;
    case 'places':
      return Number((
        item.billUnitPlacePrice +
        (item.hasLed ? (item.billUnitLedPrice || 0) * (item.billLedNum || 0) : 0) +
        (item.hasTea ? (item.billUnitTeaPrice || 0) * (item.billPersonNum || 0) : 0)
      ));
    case 'caterings':
    case 'vehicles':
    case 'attendants':
    case 'activities':
      return item.billUnitPrice || 0;
    default:
      return 0;
  }
};

// 🔥 新增：根据数据类型获取账单数量的辅助函数
const getBillQuantityByType = (item: any, type: string): number => {
  switch (type) {
    case 'stays':
      return item.billRoomNum || 0;
    case 'places':
      return 1;
    case 'caterings':
    case 'attendants':
    case 'activities':
      return item.billPersonNum || 0;
    case 'vehicles':
      return item.billVehicleNum || 0;
    default:
      return 1;
  }
};

// 从 demandInfo 的各个字段生成可选择的账单数据
const generateAvailableBillList = () => {
  const billList: RelatedBillItem[] = [];
  let sequenceNumber = 1;
  // 处理住宿数据

  if (props.demandInfo?.stays) {
    props.demandInfo.stays.forEach((stay: any, index: number) => {
      const stayId = stay.id || stay.tempId || stay.miceDemandStayId || stay.miceSchemeStayId || sequenceNumber;

      billList.push({
        id: `stay_${stayId}`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(stay.demandDate || ''),
        project: '住宿',
        category: '原需求', // 🔧 修复：统一使用简洁的"原需求"格式
        contractPrice: stay.schemeUnitPrice || 0,
        contractQuantity: stay.schemeRoomNum || 0,
        billPrice: stay.billUnitPrice || 0,
        billQuantity: stay.billRoomNum || 0,
        relatedBill: '',
      });
    });
  }

  // 处理会场数据
  if (props.demandInfo?.places) {
    props.demandInfo.places.forEach((place: any) => {
      const billtotal = Number((
        place.billUnitPlacePrice +
        (place.hasLed ? (place.billUnitLedPrice || 0) * (place.billLedNum || 0) : 0) +
        (place.hasTea ? (place.billUnitTeaPrice || 0) * (place.billPersonNum || 0) : 0)
      ))
      const schemetotal = Number((
        place.schemeUnitPlacePrice +
        (place.hasLed ? (place.schemeUnitLedPrice || 0) * place.schemeLedNum : 0) +
        (place.hasTea ? place.teaEachTotalPrice * place.schemePersonNum : 0)
      ))
      
      // 🔧 修复：会场显示单价而不是总价，保持与列名"单价金额"一致
      billList.push({
        id: `place_${place.id || place.tempId || place.miceDemandPlaceId || place.miceSchemePlaceId || sequenceNumber}`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(place.demandDate || ''),
        project: '会场',
        category: '原需求',
        contractPrice: schemetotal, // 🔧 修复：只显示会场单价
        contractQuantity: 1, // 会场通常按天计算
        billPrice: billtotal, // 🔧 修复：只显示会场单价
        billQuantity: 1,
        relatedBill: '',
      });
    });
  }

  // 处理用餐数据
  if (props.demandInfo?.caterings) {
    props.demandInfo.caterings.forEach((catering: any) => {
      billList.push({
        id: `catering_${catering.id ||
          catering.tempId ||
          catering.miceDemandCateringId ||
          catering.miceSchemeCateringId ||
          sequenceNumber
          }`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(catering.demandDate || ''),
        project: '用餐',
        category: '原需求',
        contractPrice: catering.schemeUnitPrice || 0,
        contractQuantity: catering.schemePersonNum || 0,
        billPrice: catering.billUnitPrice || 0,
        billQuantity: catering.billPersonNum || 0,
        relatedBill: '',
      });
    });
  }

  // 处理用车数据
  if (props.demandInfo?.vehicles) {
    props.demandInfo.vehicles.forEach((vehicle: any) => {
      billList.push({
        id: `vehicle_${vehicle.id || vehicle.tempId || vehicle.miceDemandVehicleId || vehicle.miceSchemeVehicleId || sequenceNumber
          }`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(vehicle.demandDate || ''),
        project: '用车',
        category: '原需求',
        contractPrice: vehicle.schemeUnitPrice || 0,
        contractQuantity: vehicle.schemeVehicleNum || 1,
        billPrice: vehicle.billUnitPrice || 0,
        billQuantity: vehicle.billVehicleNum || 1,
        relatedBill: '',
      });
    });
  }

  // 处理服务人员数据
  if (props.demandInfo?.attendants) {
    props.demandInfo.attendants.forEach((attendant: any) => {
      billList.push({
        id: `attendant_${attendant.id ||
          attendant.tempId ||
          attendant.miceDemandAttendantId ||
          attendant.miceSchemeAttendantId ||
          sequenceNumber
          }`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(attendant.demandDate || ''),
        project: '服务人员',
        category: '原需求',
        contractPrice: attendant.schemeUnitPrice || 0,
        contractQuantity: attendant.schemePersonNum || 1,
        billPrice: attendant.billUnitPrice || 0,
        billQuantity: attendant.billPersonNum || 1,
        relatedBill: '',
      });
    });
  }

  // 处理活动数据
  if (props.demandInfo?.activities) {
    props.demandInfo.activities.forEach((activity: any) => {
      billList.push({
        id: `activity_${activity.id ||
          activity.tempId ||
          activity.miceDemandActivityId ||
          activity.miceSchemeActivityId ||
          sequenceNumber
          }`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(activity.demandDate || ''),
        project: '拓展活动', // 🔧 修改：活动改为拓展活动
        category: '原需求',
        contractPrice: activity.schemeUnitPrice || 0,
        contractQuantity: activity.schemePersonNum || 1,
        billPrice: activity.billUnitPrice || 0,
        billQuantity: activity.billPersonNum || 1,
        relatedBill: '',
      });
    });
  }

  // 🔧 新增：处理礼品数据
  if (props.demandInfo?.presents) {
    props.demandInfo.presents.forEach((present: any) => {
      // 礼品可能有多个明细，每个明细作为一个账单项
      if (present.presentDetails && present.presentDetails.length > 0) {
        present.presentDetails.forEach((detail: any) => {
          billList.push({
            id: `present_${present.id || present.tempId || sequenceNumber}_${detail.id || detail.tempId || Date.now()}`,
            sequenceNumber: sequenceNumber++,
            date: formatDate(present.demandDate || ''),
            project: '礼品',
            category: detail.presentName || '礼品明细',
            contractPrice: detail.schemeUnitPrice || 0,
            contractQuantity: detail.schemeNum || 1,
            billPrice: detail.billUnitPrice || 0,
            billQuantity: detail.billPersonNum || 1,
            relatedBill: '',
          });
        });
      } else {
        // 如果没有明细，使用礼品主记录
        billList.push({
          id: `present_${present.id || present.tempId || sequenceNumber}`,
          sequenceNumber: sequenceNumber++,
          date: formatDate(present.demandDate || ''),
          project: '礼品',
          category: '礼品方案',
          contractPrice: present.schemeTotalPrice || 0,
          contractQuantity: 1,
          billPrice: present.billTotalPrice || 0,
          billQuantity: 1,
          relatedBill: '',
        });
      }
    });
  }

  // 🔧 新增：处理其他方案数据
  if (props.demandInfo?.others && props.demandInfo?.others.length > 0) {
    props.demandInfo.others.forEach((other: any) => {
      billList.push({
        id: `other_${other.id || other.tempId || sequenceNumber}`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(other.demandDate || ''),
        project: '其他',
        category: other.otherName || '其他方案',
        contractPrice: other.schemeTotalPrice || 0,
        contractQuantity: other.schemeNum || 1,
        billPrice: other.billTotalPrice,
        billQuantity: 1,
        relatedBill: '',
      });
    });
  }

  // 🔧 新增：处理补充条目数据（只显示有实际内容的条目）
  if (props.demandInfo?.additionalItems) {
    props.demandInfo.additionalItems.forEach((item: any) => {
      // 🔧 修改：只有当补充条目有实际内容时才显示
      // 检查是否有项目名称、类型、数量、单价等关键信息
      const hasContent = item.itemName ||
                        item.type !== null && item.type !== undefined ||
                        item.billNum ||
                        item.billUnitPrice ||
                        item.description;

      // 如果没有任何实际内容，跳过这个条目
      if (!hasContent) {
        return;
      }

      // 🔧 修复：使用枚举获取项目类型的文本显示
      const typeEnum = SupplementTypeConstant.ofType(item.type);
      const projectName = typeEnum ? typeEnum.desc : item.itemName || '补充项目';

      billList.push({
        id: item.tempId || `additionalItem_${item.id || sequenceNumber}`,
        sequenceNumber: sequenceNumber++,
        date: formatDate(item.occurDate || item.demandDate || ''), // 🔧 修复：使用 occurDate
        project: projectName, // 🔧 使用枚举的文本显示（如"客损"、"其他"等）
        category: '补充条目',
        contractPrice: item.billUnitPrice || 0, // 🔧 修复：使用 billUnitPrice
        contractQuantity: item.billNum || 1, // 🔧 修复：使用 billNum
        billPrice: item.billUnitPrice || 0, // 🔧 修复：使用 billUnitPrice
        billQuantity: item.billNum || 1, // 🔧 修复：使用 billNum
        relatedBill: '',
      });
    });
  }

  // 🔧 新增：处理布展物料数据（只显示总计，不显示明细）
  if (props.demandInfo?.schemeMaterial && props.demandInfo?.schemeMaterial.length > 0) {
    const schemeMaterial = props.demandInfo.schemeMaterial;

    // 🔧 只处理布展物料主对象（包含 invoiceTempId 和 statementTempId）
    billList.push({
      id: `material_main_${schemeMaterial.miceSchemeMaterialId || schemeMaterial.id || sequenceNumber}`,
      sequenceNumber: sequenceNumber++,
      date: '', // 布展物料通常没有具体日期
      project: '布展物料',
      category: '布展物料',
      contractPrice: schemeMaterial.schemeTotalPrice || 0,
      contractQuantity: 1,
      billPrice: schemeMaterial.billTotalPrice || 0,
      billQuantity: 1,
      relatedBill: '',
    });
  }

  // 🔧 新增：处理全单服务费数据
  if (props.demandInfo?.serviceFee) {
    const serviceFee = props.demandInfo.serviceFee;
    billList.push({
      id: `serviceFee_${serviceFee.id || serviceFee.tempId || 'main'}`,
      sequenceNumber: sequenceNumber++,
      date: '', // 全单服务费通常没有具体日期
      project: '全单服务费',
      category: '服务费',
      contractPrice: serviceFee.schemeServiceFeeReal || 0,
      contractQuantity: 1,
      billPrice: serviceFee.billServiceFeeReal || 0,
      billQuantity: 1,
      relatedBill: '',
    });
  }

  availableBillList.value = billList;
};

// 方法
const handleCancel = () => {
  emit('update:visible', false);
  isAddMode.value = false;
  selectedRowKeys.value = [];
  resetFilter();
  // 🔧 修复：重置分页到第一页
  resetPagination();
};

const handleAddRelatedBill = () => {
  isAddMode.value = true;
  // 自动选中已添加过的数据
  selectedRowKeys.value = [...addedBillIds.value];
};

const handleBackToView = () => {
  isAddMode.value = false;
  selectedRowKeys.value = [];
  resetFilter();
  // 🔧 修复：重置分页到第一页
  resetPagination();
};

const handleConfirmAdd = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一条记录');
    return;
  }

  const selectedItems = filteredAvailableBillList.value.filter((item) => selectedRowKeys.value.includes(item.id));

  // 只添加新选中的数据（排除已存在的）
  const newSelectedItems = selectedItems.filter((item) => !addedBillIds.value.includes(item.id));

  // 🔧 修复：如果没有新增项目，给出提示并返回
  if (newSelectedItems.length === 0) {
    message.warning('所选项目已经关联，请选择其他项目');
    return;
  }

  // 添加到关联账单列表
  const newItems = newSelectedItems.map((item) => ({
    ...item,
    id: `new_${Date.now()}_${item.id}`,
    originalId: item.id, // 🔧 新增：保留原始ID
    sequenceNumber: relatedBillList.value.length + newSelectedItems.indexOf(item) + 1,
    relatedBill: '查看>>',
  }));

  relatedBillList.value.push(...newItems);

  // 🔧 修复：只记录新增的项目ID，而不是所有选中的ID
  const newItemIds = newSelectedItems.map(item => item.id);
  addedBillIds.value = [...new Set([...addedBillIds.value, ...newItemIds])];

  // 根据发票或水单类型，将tempId关联到选中的记录
  if (props.billData?.tempId) {
    // 🔧 修复：按项目类型分组新增的ID，而不是所有选中的ID
    const newItemsByType = {
      stays: newItemIds.filter((id) => id.startsWith('stay_')),
      places: newItemIds.filter((id) => id.startsWith('place_')),
      caterings: newItemIds.filter((id) => id.startsWith('catering_')),
      vehicles: newItemIds.filter((id) => id.startsWith('vehicle_')),
      attendants: newItemIds.filter((id) => id.startsWith('attendant_')),
      activities: newItemIds.filter((id) => id.startsWith('activity_')),
      materials: newItemIds.filter((id) => id.startsWith('material_')), // 🔧 新增：布展物料（包含主对象和明细）
      presents: newItemIds.filter((id) => id.startsWith('present_')), // 🔧 新增：礼品
      others: newItemIds.filter((id) => id.startsWith('other_')), // 🔧 新增：其他
      additionalItems: newItemIds.filter((id) => id.startsWith('additionalItem_')), // 🔧 新增：其他
      serviceFees: newItemIds.filter((id) => id.startsWith('serviceFee_')), // 🔧 新增：全单服务费
    };

    // 发送关联更新事件，包含所有类型的数据
    emit('updateStaysInvoiceId', {
      invoiceTempId: props.billData.tempId,
      billType: props.billType, // 'invoice' 或 'waterBill'
      selectedItemsByType: newItemsByType,
    });

    // 计算总金额并发送更新关联金额事件，同时传递关联账单数据
    const totalAmount = relatedBillList.value.reduce((sum, item) => sum + Number(item.billPrice) * item.billQuantity, 0);

    emit('updateRelatedAmount', {
      invoiceTempId: props.billData.tempId,
      billType: props.billType,
      totalAmount: totalAmount,
      relatedBills: [...relatedBillList.value], // 传递完整的关联账单数据
    });

    // 🔧 新增：成功提示
    message.success(`成功添加 ${newSelectedItems.length} 项关联账单`);
  }

  // 返回查看模式
  handleBackToView();
};

const resetFilter = () => {
  filterForm.value = {
    hotelPlan: '',
    project: '',
    planDate: null,
  };
};

// 🔧 新增：删除关联账单
const handleDeleteRelatedBill = (record: RelatedBillItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除这条关联账单吗？\n项目：${record.project}\n类别：${record.category}`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      // 从关联账单列表中移除
      const index = relatedBillList.value.findIndex((item) => item.id === record.id);
      if (index > -1) {
        relatedBillList.value.splice(index, 1);

        // 重新计算序号
        relatedBillList.value.forEach((item, idx) => {
          item.sequenceNumber = idx + 1;
        });

        // 从已添加ID列表中移除（提取原始ID）
        const originalId = extractOriginalBillId(record.id);
        const addedIndex = addedBillIds.value.indexOf(originalId);
        if (addedIndex > -1) {
          addedBillIds.value.splice(addedIndex, 1);
        }

        // 🔧 修复：删除操作需要精确清除被删除项目的关联ID
        // 但不能影响其他项目的关联ID
        if (props.billData?.tempId) {
          // 构造要清除关联的数据结构（只包含被删除的项目）
          const selectedItemsByType = {
            stays: originalId.startsWith('stay_') ? [originalId] : [],
            places: originalId.startsWith('place_') ? [originalId] : [],
            caterings: originalId.startsWith('catering_') ? [originalId] : [],
            vehicles: originalId.startsWith('vehicle_') ? [originalId] : [],
            attendants: originalId.startsWith('attendant_') ? [originalId] : [],
            activities: originalId.startsWith('activity_') ? [originalId] : [],
            materials: originalId.startsWith('material_') ? [originalId] : [], // 🔧 新增：布展物料
            presents: originalId.startsWith('present_') ? [originalId] : [],
            others: originalId.startsWith('other_') ? [originalId] : [],
            additionalItems: originalId.startsWith('additionalItem_') ? [originalId] : [], // 🔧 新增：补充条目
            serviceFees: originalId.startsWith('serviceFee_') ? [originalId] : [],
          };

          // 🔧 新增：发送特殊的删除事件，使用特殊标识来区分删除操作
          emit('updateStaysInvoiceId', {
            invoiceTempId: `DELETE_${props.billData.tempId}`, // 使用特殊前缀标识删除操作
            billType: props.billType,
            selectedItemsByType: selectedItemsByType,
          });
        }

        // 更新关联金额和账单数据
        if (props.billData?.tempId) {
          const totalAmount = relatedBillList.value.reduce((sum, item) => sum + Number(item.billPrice) * item.billQuantity, 0);

          emit('updateRelatedAmount', {
            invoiceTempId: props.billData.tempId,
            billType: props.billType,
            totalAmount: totalAmount,
            relatedBills: [...relatedBillList.value],
          });
        }
      }
    },
  });
};

// 🔧 新增：根据类型获取项目名称
const getProjectNameByType = (type: string): string => {
  const typeMap: Record<string, string> = {
    stay: '住宿',
    place: '会场',
    catering: '用餐',
    vehicle: '用车',
    attendant: '服务人员',
    activity: '拓展活动',
    material: '布展物料', // 🔧 新增：布展物料
    present: '礼品',
    other: '其他',
    additionalItem: '补充条目',
    serviceFee: '全单服务费'
  };
  return typeMap[type];
};

// itemData.billNum ? Number((Number(itemData.billTotalPrice) / Number(itemData.billNum)).toFixed(2)) : 0;
// 🔧 新增：根据类型和数据获取合同价格
const getContractPrice = (itemData: any, type: string): number => {
  if (!itemData) return 0;

  switch (type) {
    case 'stay':
      return itemData.schemeUnitPrice || 0;
    case 'place':
      // 🔧 修复：会场合同单价，不是总价
      return Number((
        itemData.schemeUnitPlacePrice +
        (itemData.hasLed ? (itemData.schemeUnitLedPrice || 0) * itemData.schemeLedNum : 0) +
        (itemData.hasTea ? itemData.teaEachTotalPrice * itemData.schemePersonNum : 0)
      ))
    case 'catering':
    case 'vehicle':
    case 'attendant':
    case 'activity':
      return itemData.schemeUnitPrice;
    case 'material': // 🔧 新增：布展物料
      return itemData.schemeUnitPrice || 0;
    case 'present':
      return itemData.schemeTotalPrice || itemData.schemeUnitPrice;
    case 'other':
      return itemData.schemeTotalPrice;
    case 'additionalItem':
      return itemData.billUnitPrice;
    case 'serviceFee':
      return itemData.schemeServiceFeeReal;
    default:
      return 200;
  }
};

// 🔧 新增：根据类型和数据获取合同数量
const getContractQuantity = (itemData: any, type: string): number => {
  if (!itemData) return 0;

  switch (type) {
    case 'stay':
      return itemData.schemeRoomNum;
    case 'place':
      return 1; // 会场通常按天计算
    case 'catering':
      return itemData.schemePersonNum;
    case 'vehicle':
      return itemData.schemeVehicleNum;
    case 'attendant':
    case 'activity':
      return itemData.schemePersonNum;
    case 'material': // 🔧 新增：布展物料
      return itemData.schemeMaterialNum || 1;
    case 'present':
      return itemData.schemeNum;
    case 'other':
      return itemData.num;
    case 'additionalItem':
      return itemData.billNum;
    case 'serviceFee':
      return 1;
    default:
      return 1;
  }
};

// 🔧 新增：根据类型和数据获取账单价格
const getBillPrice = (itemData: any, type: string, fallbackAmount?: number): number => {
  if (!itemData) return 0;

  switch (type) {
    case 'stay':
      return itemData.billUnitPrice;
    case 'place':
      // 🔧 修复：账单会场单价，不是总价
      return Number((
        itemData.billUnitPlacePrice +
        (itemData.hasLed ? (itemData.billUnitLedPrice || 0) * (itemData.billLedNum || 0) : 0) +
        (itemData.hasTea ? (itemData.billUnitTeaPrice || 0) * (itemData.billPersonNum || 0) : 0)
      ));
    case 'catering':
    case 'vehicle':
    case 'attendant':
    case 'activity':
      return itemData.billUnitPrice || fallbackAmount || 0;
    case 'material': // 🔧 新增：布展物料
      return itemData.billUnitPrice || fallbackAmount || 0;
    case 'present':
      // 🔧 修复：礼品账单单价，优先使用单价而不是总价
      return itemData.billUnitPrice || fallbackAmount || 0;
    case 'other':
      return itemData.billNum ? Number((Number(itemData.billTotalPrice) / Number(itemData.billNum)).toFixed(2)) : 0;
    case 'additionalItem':
      return itemData.billUnitPrice || 0;
    case 'serviceFee':
      return itemData.billServiceFeeReal || 0;
    default:
      return 0;
  }
};

// 🔧 新增：根据类型和数据获取账单数量
const getBillQuantity = (itemData: any, type: string): number => {
  if (!itemData) return 0;

  switch (type) {
    case 'stay':
      return itemData.billRoomNum;
    case 'place':
      return 1;
    case 'catering':
      return itemData.billPersonNum;
    case 'vehicle':
      return itemData.billVehicleNum;
    case 'attendant':
    case 'activity':
      return itemData.billPersonNum;
    case 'material': // 🔧 新增：布展物料
      return itemData.billMaterialNum || 1;
    case 'present':
      return itemData.billPersonNum;
    case 'other':
      return itemData.billNum;
    case 'additionalItem':
      return itemData.billNum || 0;
    case 'serviceFee':
      return 1;
    default:
      return 0;
  }
};

// 🔧 新增：提取原始账单ID的辅助函数
const extractOriginalBillId = (billId: string): string => {
  // 如果是新添加的格式 'new_timestamp_originalId'，提取原始ID
  if (billId.startsWith('new_')) {
    const parts = billId.split('_');
    if (parts.length >= 3) {
      return parts.slice(2).join('_'); // 去掉 'new_' 和时间戳部分
    }
  }
  return billId;
};

// 🔧 新增：重置分页的方法
const resetPagination = () => {
  pagination.value.current = 1;
  pagination.value.pageSize = 10;
};

const toggleSelectAll = () => {
  const currentPageData = getCurrentPageData();
  const currentPageIds = currentPageData.map((item) => item.id);

  if (isAllSelected.value) {
    // 取消当前页所有选择
    selectedRowKeys.value = selectedRowKeys.value.filter((id) => !currentPageIds.includes(id));
  } else {
    // 选中当前页所有项（保留其他页的选择）
    const newSelectedKeys = [...new Set([...selectedRowKeys.value, ...currentPageIds])];
    selectedRowKeys.value = newSelectedKeys;
  }
};

const handleSearch = () => {
  // 查询逻辑，这里可以添加搜索过滤功能

  // 重置分页到第一页
  pagination.value.current = 1;
  // 保持选择状态，不清空
  message.success(`查询完成，找到 ${filteredAvailableBillList.value.length} 条记录`);
};

const handleSelectChange = (selectedKeys: string[]) => {
  selectedRowKeys.value = selectedKeys;
};

// 分页变化处理
const handleTableChange = (pag: any) => {
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
  // 不清空选择状态，保持跨页选择
};

// 格式化金额显示
const formatAmount = (amount: number) => {
  return `${formatNumberThousands(amount, true)}元`;
};

// 监听attachmentTotalAmount变化，同步到可编辑金额（仅在没有初始金额时）
watch(
  attachmentTotalAmount,
  (newValue) => {
    // 🔧 修改：只有在没有传入初始金额时才自动同步计算值
    if (props.billData?.initialAttachmentAmount === undefined) {
      editableAttachmentAmount.value = newValue;
    }
  },
  { immediate: true },
);

// 🔧 新增：监听用户修改附件金额
const handleAttachmentAmountChange = () => {
  // 当用户修改金额时，通知父组件更新
  if (props.billData?.tempId && typeof editableAttachmentAmount.value === 'number') {
    emit('updateAttachmentAmount', {
      invoiceTempId: props.billData.tempId,
      billType: props.billType,
      attachmentAmount: editableAttachmentAmount.value,
    });
  }
};

// 初始化已有的关联账单数据
const initializeExistingRelatedBills = () => {
  // 在查看模式下，根据发票/水单ID自动筛选关联账单
  if (props.billData && (props.billData.id || props.billData.tempId)) {
    // 提取发票/水单的ID（优先使用id，其次使用tempId中的数字部分）
    let invoiceId = props.billData.id;
    if (!invoiceId && props.billData.tempId) {
      // 从tempId中提取数字ID，例如从 "invoice_1234_567" 中提取 "1234"
      const tempIdParts = props.billData.tempId.split('_');
      if (tempIdParts.length >= 2) {
        invoiceId = tempIdParts[1];
      }
    }

    if (invoiceId) {
      // 根据发票/水单ID自动筛选关联的账单
      const autoRelatedBills = getRelatedBillsByInvoiceId(invoiceId, props.billType);

      if (autoRelatedBills.length > 0) {
        relatedBillList.value = autoRelatedBills;

        // 记录已添加的账单ID
        addedBillIds.value = autoRelatedBills.map(bill => bill.id);

        return; // 使用自动筛选的数据，不再处理existingRelatedBills
      }
    }
  }

  // 原有逻辑：使用传入的existingRelatedBills
  if (props.existingRelatedBills && props.existingRelatedBills.length > 0) {
    const existingRelatedBills = JSON.parse(JSON.stringify(props.existingRelatedBills))
    relatedBillList.value = existingRelatedBills.map((item, index) => ({
      id: item.id,
      sequenceNumber: index + 1,
      date: formatDate(item.itemData?.demandDate || '') || item.date,
      project: getProjectNameByType(item.type) || item.project,
      category: item.name || '原需求' || item.name,
      contractPrice: getContractPrice(item.itemData, item.type) || item.contractPrice || 0,
      contractQuantity: getContractQuantity(item.itemData, item.type) || item.contractQuantity || 0,
      billPrice: getBillPrice(item.itemData, item.type, item.amount) || item.billPrice || 0,
      billQuantity: getBillQuantity(item.itemData, item.type) || item.billQuantity || 0,
      itemData: item.itemData || null,
      type: item.type
    }));
  
    // 记录已添加的账单ID，防止重复添加
    addedBillIds.value = existingRelatedBills.map((bill) => {
      // 从bill.id中提取原始ID
      if (bill.id.startsWith('new_')) {
        // 如果是新添加的格式，提取原始ID
        const parts = bill.id.split('_');
        if (parts.length >= 3) {
          return parts.slice(2).join('_'); // 去掉 'new_' 和时间戳部分
        }
      }
      return bill.id;
    });
  } else {
    relatedBillList.value = [];
    addedBillIds.value = [];
  }
};

// 监听 demandInfo 变化
watch(
  () => props.demandInfo,
  (newValue) => {
    if (newValue) {
      // 生成可选择的账单数据
      generateAvailableBillList();
    }
  },
  { immediate: true, deep: true },
);

// 监听 existingRelatedBills 变化，但不要在用户操作过程中重置数据
watch(
  () => props.existingRelatedBills,
  (newValue, oldValue) => {

    // 只有在弹窗首次打开或者外部数据真正变化时才初始化
    if (!isAddMode.value && relatedBillList.value.length === 0) {
      initializeExistingRelatedBills();
    }
  },
  { immediate: true, deep: true },
);

// 监听弹窗显示状态，每次打开时重新初始化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 优先使用传入的初始金额，否则使用发票/水单的总金额，最后使用计算值
      if (props.billData?.initialAttachmentAmount !== undefined) {
        editableAttachmentAmount.value = props.billData.initialAttachmentAmount;
      } else if (props.billData?.totalAmountCny !== undefined && props.billData?.totalAmountCny !== null) {
        // 从发票/水单的总金额获取附件总金额
        editableAttachmentAmount.value = props.billData.totalAmountCny;
      } else {
        editableAttachmentAmount.value = attachmentTotalAmount.value;
      }

      initializeExistingRelatedBills();
      // 🔧 修复：每次打开弹框都重置到第一页
      resetPagination();
      // 🔧 修复：重置筛选条件
      resetFilter();
    }
  },
  { immediate: true },
);

watch(
  () => props.excludedBillIds,
  (newExcludedIds) => { },
  { immediate: true, deep: true },
);
</script>

<template>
  <a-modal :open="visible" :title="modalTitle" width="1400px" :footer="null"
    :body-style="{ overflow: 'auto', padding: '20px' }" @cancel="handleCancel">
    <!-- 查看模式 -->
    <div v-if="!isAddMode" class="view-mode">
      <!-- 金额统计 -->
      <div class="amount-summary">
        <span class="amount-item">
          附件总金额:
          <!-- 查看模式下显示只读文本 -->
          <span v-if="isViewOnlyMode" class="amount-value">{{ formatAmount(editableAttachmentAmount) }}</span>
          <!-- 编辑模式下显示可编辑输入框 -->
          <template v-else>
            <input v-model.number="editableAttachmentAmount" class="amount-input" type="number" :min="0" :step="0.01"
              placeholder="请输入金额" @blur="handleAttachmentAmountChange" @change="handleAttachmentAmountChange" />元
          </template>
        </span>
        <span class="amount-item">
          账单合计金额: <span class="amount-value red">{{ formatAmount(billTotalAmount) }}</span>
        </span>
      </div>

      <!-- 关联账单表格 -->
      <div class="table-wrapper">
        <a-table :columns="viewColumns" :data-source="relatedBillList" :pagination="false" size="small"
          :scroll="{ x: 1000, y: 300 }" row-key="id">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'contractPrice'">
              {{ formatAmount(record.contractPrice) }}
            </template>
            <template v-else-if="column.dataIndex === 'billPrice'">
              {{ formatAmount(record.billPrice) }}
            </template>
            <template v-else-if="column.dataIndex === 'relatedBill'">
              <a-button type="link" size="small">
                {{ record.relatedBill }}
              </a-button>
            </template>
            <!-- 🔧 新增：操作列 - 删除按钮（查看模式下隐藏） -->
            <template v-else-if="column.dataIndex === 'action'">
              <a-button
                v-if="!isViewOnlyMode"
                type="link"
                size="small"
                danger
                @click="() => handleDeleteRelatedBill(record)"
              >
                删除
              </a-button>
              <span v-else class="readonly-text">-</span>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 添加按钮（查看模式下隐藏） -->
      <div v-if="!isViewOnlyMode" class="add-button-wrapper">
        <a-button type="link" @click="handleAddRelatedBill"> + 增加账单关联 </a-button>
      </div>
    </div>

    <!-- 添加模式 -->
    <div v-else class="add-mode">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <a-row :align="'middle'">
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label>方案酒店：</label>
          </a-col>
          <a-col :span="4">
            <a-input v-model:value="filterForm.hotelPlan" placeholder="请输入" allow-clear />
          </a-col>
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label>类型：</label>
          </a-col>
          <a-col :span="4">
            <a-select v-model:value="filterForm.project" :options="projectOptions" placeholder="请选择" allow-clear
              style="width: 100%" />
          </a-col>
          <a-col :span="2" style="text-align: right; padding-right: 10px">
            <label>方案日期：</label>
          </a-col>
          <a-col :span="4">
            <a-date-picker v-model:value="filterForm.planDate" placeholder="请选择" allow-clear style="width: 100%" />
          </a-col>
          <a-col :span="6" style="text-align: left; padding-left: 10px">
            <a-button style="margin-right: 10px" @click="resetFilter">重置</a-button>
            <a-button type="primary" @click="handleSearch">查询</a-button>
          </a-col>
        </a-row>
        <a-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <a-col :span="18" style="text-align: left">
            <a-button @click="toggleSelectAll">{{ isAllSelected ? '取消全选' : '全选' }}</a-button>
            <!-- <span v-if="props.excludedBillIds && props.excludedBillIds.length > 0" style="margin-left: 16px; color: #666; font-size: 12px;">
              已过滤 {{ props.excludedBillIds.length }} 条已关联的账单
            </span> -->
          </a-col>
          <!-- <a-col :span="6" style="text-align: right; color: #666; font-size: 12px;">
            共 {{ filteredAvailableBillList.length }} 条可选账单
          </a-col> -->
        </a-row>
      </div>

      <!-- 可选择账单表格 -->
      <div class="table-wrapper">
        <a-table :columns="addColumns" :data-source="filteredAvailableBillList" :pagination="pagination" size="small"
          :scroll="{ x: 1000, y: 500 }" row-key="id" :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: handleSelectChange,
            type: 'checkbox',
          }" @change="handleTableChange">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'contractPrice'">
              {{ formatAmount(record.contractPrice) }}
            </template>
            <template v-else-if="column.dataIndex === 'billPrice'">
              {{ formatAmount(record.billPrice) }}
            </template>
          </template>
        </a-table>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-button @click="handleBackToView"> 返回 </a-button>
        <a-button type="primary" @click="handleConfirmAdd"> 确认添加 </a-button>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
.view-mode {
  .amount-summary {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #fafafa;
    border-radius: 4px;
    display: flex;
    gap: 32px;

    .amount-item {
      font-size: 14px;
      color: #333;

      .amount-value {
        font-weight: 600;
        margin-left: 4px;

        &.red {
          color: #ff4d4f;
        }
      }

      .amount-input {
        border: none;
        border-bottom: 1px solid #d9d9d9;
        background: transparent;
        outline: none;
        font-weight: 600;
        margin: 0 4px;
        padding: 2px 4px;
        min-width: 80px;
        text-align: center;

        &:focus {
          border-bottom: 2px solid #1890ff;
        }

        &::placeholder {
          color: #bfbfbf;
          font-weight: normal;
        }
      }

    }
  }

  .add-button-wrapper {
    margin-top: 16px;
    text-align: center;
    padding: 12px;
    border-top: 1px solid #d9d9d9;
    background-color: #fafafa;
  }
}

.add-mode {
  .filter-section {
    margin-bottom: 16px;
    border-radius: 4px;
  }

  .action-buttons {
    margin-top: 16px;
    text-align: center;
    display: flex;
    justify-content: center;
    gap: 16px;
  }
}

.table-wrapper {
  :deep(.ant-table) {
    .ant-table-thead>tr>th {
      background-color: #fafafa;
      font-weight: 500;
      text-align: center;
    }

    .ant-table-tbody>tr>td {
      text-align: center;
    }
  }
}
</style>
